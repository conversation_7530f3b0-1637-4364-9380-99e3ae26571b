// styled components
import { DateInputWrapper } from "./style";
import { Input } from "@ui/Field";

// components
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";

// utils
import { useRef, useState } from "react";
import moment from "moment";

const DateInput = ({ id, maxDate, onChange, placeholder, value, minDate, disabled }) => {
  const [open, setOpen] = useState(false);
  const customInputRef = useRef(null);
  const buttonRef = useRef(null);

  return (
    <LocalizationProvider dateAdapter={AdapterMoment}>
      <DatePicker
        open={open}
        value={value}
        maxDate={maxDate}
        minDate={minDate}
        onChange={(newValue) => {
          onChange(newValue);
        }}
        disableHighlightToday
        disabled={disabled}
        onClose={() => setOpen(false)}
        PopperProps={{ anchorEl: customInputRef.current, sx: { zIndex: '20000 !important' } }}
        PaperProps={{ className: "date-picker" }}
        renderInput={({ ref, inputProps, disabled, onChange, value }) => (
          <DateInputWrapper ref={ref}>
            <Input
              id={id}
              value={value && moment(value).format("MMM DD, YYYY")}
              onChange={onChange}
              disabled={disabled}
              ref={customInputRef}
              placeholder={placeholder}
              {...inputProps}
              onClick={() => setOpen(true)}
            />
            <i className="icon icon-calendar" ref={buttonRef} onClick={() => setOpen(true)} />
          </DateInputWrapper>
        )}
      />
    </LocalizationProvider>
  );
};

export default DateInput;
