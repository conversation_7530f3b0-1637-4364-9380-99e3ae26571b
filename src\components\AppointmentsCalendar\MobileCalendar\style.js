import styled from "styled-components/macro";
import { flex, colors, textSizes, dark, light, breakpoints } from "@styles/vars";
import theme from "styled-theming";

const borderColor = theme("theme", {
  light: "#DCE2E8",
  dark: "#25292D",
});

const bgColor = theme("theme", {
  light: light.widgetBg,
  dark: dark.widgetBg,
});

const textColor = theme("theme", {
  light: light.text,
  dark: dark.text,
});

const highlightBg = theme("theme", {
  light: light.highlight,
  dark: dark.highlight,
});

export const Container = styled.div`
  background: ${bgColor};
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
`;

export const WeekDays = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
  
  .weekday {
    text-align: center;
    font-size: ${textSizes["12"]};
    font-weight: 600;
    color: ${colors.gray};
    padding: 8px 4px;
    text-transform: uppercase;
  }
`;

export const CalendarGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
`;

export const DayCell = styled.div`
  position: relative;
  aspect-ratio: 1;
  ${flex.center};
  flex-direction: column;
  border-radius: 6px;
  cursor: ${props => props.selectable ? 'pointer' : 'default'};
  transition: all 0.2s ease;
  background-color: ${props => {
    if (props.isSelected) return colors.blue;
    if (props.isToday) return highlightBg;
    return 'transparent';
  }};
  
  opacity: ${props => {
    if (!props.isCurrentMonth) return 0.3;
    if (!props.selectable) return 0.5;
    return 1;
  }};
  
  &:hover {
    background-color: ${props => {
      if (!props.selectable) return 'transparent';
      if (props.isSelected) return colors.blue;
      return highlightBg;
    }};
  }
  
  .day-number {
    font-size: ${textSizes["14"]};
    font-weight: ${props => props.isToday ? '600' : '400'};
    color: ${props => {
      if (props.isSelected) return '#fff';
      if (props.isToday) return colors.blue;
      return textColor;
    }};
    margin-bottom: 2px;
  }
  
  .event-indicator {
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: ${props => props.isSelected ? '#fff' : colors.blue};
  }
  
  ${breakpoints.mobileS} {
    min-height: 40px;
    
    .day-number {
      font-size: ${textSizes["13"]};
    }
  }
  
  ${breakpoints.mobileL} {
    min-height: 44px;
    
    .day-number {
      font-size: ${textSizes["14"]};
    }
  }
`;
