// styled components
import { Container, CalendarGrid, DayCell, WeekDays } from "./style";

// utils
import moment from "moment";

const MobileCalendar = ({
  currentDate,
  onDateClick,
  appointments = [],
  selectedPatient,
  minDate,
  maxDate,
  viewType = "month"
}) => {
  // Use currentDate directly since navigation is handled by parent
  const viewDate = moment(currentDate);

  // Generate calendar days based on view type
  const calendarDays = [];

  if (viewType === "month") {
    // For month view, show only the first week that contains the current date
    const startOfWeek = viewDate.clone().startOf('week');
    let day = startOfWeek.clone();

    for (let i = 0; i < 7; i++) {
      calendarDays.push(day.clone());
      day.add(1, 'day');
    }
  } else if (viewType === "week") {
    // For week view, show the current week
    const startOfWeek = viewDate.clone().startOf('week');
    let day = startOfWeek.clone();

    for (let i = 0; i < 7; i++) {
      calendarDays.push(day.clone());
      day.add(1, 'day');
    }
  } else if (viewType === "day") {
    // For day view, show only the current day
    calendarDays.push(viewDate.clone());
  }

  // Check if a date has appointments
  const hasAppointments = (date) => {
    return appointments.some(appointment => 
      moment(appointment.start).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
    );
  };

  // Check if a date is selectable
  const isSelectable = (date) => {
    const now = moment().startOf('day');
    const selected = date.clone().startOf('day');
    
    // Don't allow past dates
    if (selected.isBefore(now)) {
      return false;
    }
    
    // Check min/max date restrictions
    if (minDate && selected.isBefore(moment(minDate).startOf('day'))) {
      return false;
    }
    
    if (maxDate && selected.isAfter(moment(maxDate).startOf('day'))) {
      return false;
    }
    
    return true;
  };

  // Handle day click
  const handleDayClick = (date) => {
    if (!isSelectable(date)) {
      return;
    }
    
    onDateClick(date.toDate());
  };

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <Container viewType={viewType}>
      {viewType !== "day" && (
        <WeekDays viewType={viewType}>
          {(viewType === "day" ? [weekDays[moment(currentDate).day()]] : weekDays).map(day => (
            <div key={day} className="weekday">
              {day}
            </div>
          ))}
        </WeekDays>
      )}

      <CalendarGrid viewType={viewType}>
        {calendarDays.map((date, index) => {
          const isCurrentMonth = date.month() === viewDate.month();
          const isToday = date.isSame(moment(), 'day');
          const isSelected = date.isSame(moment(currentDate), 'day');
          const hasEvents = hasAppointments(date);
          const selectable = isSelectable(date);

          return (
            <DayCell
              key={index}
              isCurrentMonth={isCurrentMonth}
              isToday={isToday}
              isSelected={isSelected}
              hasEvents={hasEvents}
              selectable={selectable}
              viewType={viewType}
              onClick={() => handleDayClick(date)}
            >
              <span className="day-number">
                {viewType === "day" ? date.format('dddd, MMMM DD') : date.format('D')}
              </span>
              {hasEvents && <span className="event-indicator" />}
              {viewType === "day" && hasEvents && (
                <div className="appointments-list">
                  {appointments
                    .filter(apt => moment(apt.start).format('YYYY-MM-DD') === date.format('YYYY-MM-DD'))
                    .map((apt, i) => (
                      <div key={i} className="appointment-item">
                        {moment(apt.start).format('HH:mm')} - {apt.title}
                      </div>
                    ))
                  }
                </div>
              )}
            </DayCell>
          );
        })}
      </CalendarGrid>
    </Container>
  );
};

export default MobileCalendar;
