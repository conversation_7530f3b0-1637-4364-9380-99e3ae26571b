// styled components
import { Container, CalendarGrid, DayCell, WeekDays } from "./style";

// utils
import moment from "moment";

const MobileCalendar = ({
  currentDate,
  onDateClick,
  appointments = [],
  selectedPatient,
  minDate,
  maxDate
}) => {
  // Use currentDate directly since navigation is handled by parent
  const viewDate = moment(currentDate);

  // Get the start and end of the month for the calendar grid
  const startOfMonth = viewDate.startOf('month');
  const endOfMonth = viewDate.endOf('month');
  const startOfCalendar = startOfMonth.clone().startOf('week');
  const endOfCalendar = endOfMonth.clone().endOf('week');

  // Generate calendar days
  const calendarDays = [];
  let day = startOfCalendar.clone();
  
  while (day.isSameOrBefore(endOfCalendar)) {
    calendarDays.push(day.clone());
    day.add(1, 'day');
  }

  // Check if a date has appointments
  const hasAppointments = (date) => {
    return appointments.some(appointment => 
      moment(appointment.start).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
    );
  };

  // Check if a date is selectable
  const isSelectable = (date) => {
    const now = moment().startOf('day');
    const selected = date.clone().startOf('day');
    
    // Don't allow past dates
    if (selected.isBefore(now)) {
      return false;
    }
    
    // Check min/max date restrictions
    if (minDate && selected.isBefore(moment(minDate).startOf('day'))) {
      return false;
    }
    
    if (maxDate && selected.isAfter(moment(maxDate).startOf('day'))) {
      return false;
    }
    
    return true;
  };

  // Handle day click
  const handleDayClick = (date) => {
    if (!isSelectable(date)) {
      return;
    }
    
    onDateClick(date.toDate());
  };

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <Container>
      
      <WeekDays>
        {weekDays.map(day => (
          <div key={day} className="weekday">
            {day}
          </div>
        ))}
      </WeekDays>
      
      <CalendarGrid>
        {calendarDays.map((date, index) => {
          const isCurrentMonth = date.month() === viewDate.month();
          const isToday = date.isSame(moment(), 'day');
          const isSelected = date.isSame(moment(currentDate), 'day');
          const hasEvents = hasAppointments(date);
          const selectable = isSelectable(date);
          
          return (
            <DayCell
              key={index}
              isCurrentMonth={isCurrentMonth}
              isToday={isToday}
              isSelected={isSelected}
              hasEvents={hasEvents}
              selectable={selectable}
              onClick={() => handleDayClick(date)}
            >
              <span className="day-number">{date.format('D')}</span>
              {hasEvents && <span className="event-indicator" />}
            </DayCell>
          );
        })}
      </CalendarGrid>
    </Container>
  );
};

export default MobileCalendar;
